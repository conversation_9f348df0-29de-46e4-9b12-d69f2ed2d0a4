import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  serverTimestamp
} from 'firebase/firestore';
import { db, auth } from '../config/firebase';
import type { Prompt, PromptExecution, RAGDocument, Workspace } from '../types';

// Retry utility with exponential backoff
class RetryManager {
  static async withRetry<T>(
    operation: () => Promise<T>,
    maxAttempts: number = 3,
    baseDelay: number = 1000,
    maxDelay: number = 10000
  ): Promise<T> {
    let lastError: any;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error: any) {
        lastError = error;

        // Don't retry on certain error types
        if (this.isNonRetryableError(error)) {
          throw error;
        }

        if (attempt === maxAttempts) {
          break;
        }

        // Calculate delay with exponential backoff and jitter
        const delay = Math.min(
          baseDelay * Math.pow(2, attempt - 1) + Math.random() * 1000,
          maxDelay
        );

        console.log(`⏳ Retry attempt ${attempt}/${maxAttempts} after ${delay}ms delay`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError;
  }

  private static isNonRetryableError(error: any): boolean {
    const nonRetryableCodes = [
      'permission-denied',
      'unauthenticated',
      'invalid-argument',
      'not-found',
      'already-exists',
      'failed-precondition'
    ];

    return nonRetryableCodes.includes(error.code) ||
           error.message?.includes('User ID mismatch') ||
           error.message?.includes('required and cannot be empty');
  }
}

// Enhanced validation utilities
class PromptValidator {
  static validatePromptData(promptData: Partial<Prompt>): void {
    const errors: string[] = [];

    // Title validation
    if (!promptData.title?.trim()) {
      errors.push('Title is required');
    } else if (promptData.title.length > 200) {
      errors.push('Title cannot exceed 200 characters');
    } else if (promptData.title.length < 3) {
      errors.push('Title must be at least 3 characters long');
    }

    // Content validation
    if (!promptData.content?.trim()) {
      errors.push('Content is required');
    } else if (promptData.content.length > 50000) {
      errors.push('Content cannot exceed 50,000 characters');
    } else if (promptData.content.length < 10) {
      errors.push('Content must be at least 10 characters long');
    }

    // Category validation
    if (promptData.category && promptData.category.length > 50) {
      errors.push('Category cannot exceed 50 characters');
    }

    // Tags validation
    if (promptData.tags) {
      if (promptData.tags.length > 20) {
        errors.push('Cannot have more than 20 tags');
      }
      for (const tag of promptData.tags) {
        if (tag.length > 30) {
          errors.push('Tags cannot exceed 30 characters each');
        }
      }
    }

    // Description validation
    if (promptData.description && promptData.description.length > 1000) {
      errors.push('Description cannot exceed 1000 characters');
    }

    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join(', ')}`);
    }
  }

  static sanitizePromptData(promptData: Partial<Prompt>): Partial<Prompt> {
    return {
      ...promptData,
      title: promptData.title?.trim(),
      content: promptData.content?.trim(),
      description: promptData.description?.trim(),
      category: promptData.category?.trim() || 'General',
      tags: promptData.tags?.map(tag => tag.trim()).filter(tag => tag.length > 0) || [],
      isPublic: Boolean(promptData.isPublic),
      variables: promptData.variables || []
    };
  }
}

// Prompt operations
export const promptService = {
  // Create a new prompt with validation and retry logic
  async createPrompt(userId: string, promptData: Omit<Prompt, 'id' | 'createdAt' | 'updatedAt' | 'createdBy' | 'version'>) {
    const startTime = Date.now();
    const operationId = Math.random().toString(36).substr(2, 9);

    return RetryManager.withRetry(async () => {
      console.log(`🔍 [${operationId}] Creating prompt for user:`, userId);

      // Validate user ID
      if (!userId?.trim()) {
        throw new Error('User ID is required and cannot be empty');
      }

      // Validate auth state before proceeding
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('No authenticated user found. Please sign in again.');
      }
      if (currentUser.uid !== userId) {
        throw new Error('User ID mismatch. Please sign out and sign in again.');
      }

      // Sanitize and validate prompt data
      const sanitizedData = PromptValidator.sanitizePromptData(promptData);
      PromptValidator.validatePromptData(sanitizedData);

      console.log(`📝 [${operationId}] Validated prompt data:`, {
        title: sanitizedData.title,
        contentLength: sanitizedData.content?.length || 0,
        category: sanitizedData.category,
        tagsCount: sanitizedData.tags?.length || 0,
        isPublic: sanitizedData.isPublic
      });

      const promptsRef = collection(db, 'users', userId, 'prompts');
      console.log(`📁 [${operationId}] Collection reference created:`, promptsRef.path);

      const newPrompt = {
        ...sanitizedData,
        createdBy: userId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        version: 1,
        // Add metadata for debugging
        _metadata: {
          operationId,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        }
      };

      console.log(`💾 [${operationId}] Saving prompt to Firestore...`);
      const docRef = await addDoc(promptsRef, newPrompt);

      const duration = Date.now() - startTime;
      console.log(`✅ [${operationId}] Prompt saved successfully with ID: ${docRef.id} (${duration}ms)`);

      // Verify the document was actually saved
      const savedDoc = await getDoc(docRef);
      if (!savedDoc.exists()) {
        throw new Error('Prompt was not saved properly - document does not exist after creation');
      }

      console.log(`🔍 [${operationId}] Prompt save verification successful`);
      return docRef.id;

    }, 3, 1000, 10000).catch((error: any) => {
      const duration = Date.now() - startTime;
      console.error(`❌ [${operationId}] Error creating prompt after retries (${duration}ms):`, error);
      console.error(`🔍 [${operationId}] Error details:`, {
        code: error.code,
        message: error.message,
        userId,
        promptTitle: promptData.title,
        authState: auth.currentUser ? 'authenticated' : 'not authenticated',
        authUid: auth.currentUser?.uid,
        stack: error.stack
      });

      // Enhanced error handling with specific solutions
      if (error.code === 'permission-denied') {
        throw new Error('Permission denied. Please check your authentication and try signing out and back in.');
      } else if (error.code === 'unauthenticated') {
        throw new Error('Authentication required. Please sign in and try again.');
      } else if (error.code === 'network-request-failed') {
        throw new Error('Network error. Please check your internet connection and try again.');
      } else if (error.code === 'unavailable') {
        throw new Error('Firestore service is temporarily unavailable. Please try again in a moment.');
      } else if (error.code === 'deadline-exceeded') {
        throw new Error('Request timed out. Please try again.');
      } else if (error.code === 'resource-exhausted') {
        throw new Error('Service quota exceeded. Please try again later.');
      } else if (error.code === 'failed-precondition') {
        throw new Error('Database operation failed. Please refresh the page and try again.');
      } else if (error.message.includes('auth')) {
        throw new Error('Authentication error. Please sign out and sign in again.');
      } else if (error.message.includes('User ID mismatch') || error.message.includes('Validation failed')) {
        throw error; // Re-throw validation errors as-is
      } else {
        // For unknown errors, provide debugging information
        const debugInfo = `Error ID: ${operationId}, Time: ${new Date().toISOString()}`;
        throw new Error(`${error.message || 'Failed to save prompt'} (${debugInfo})`);
      }
    });
  },

  // Get a specific prompt
  async getPrompt(userId: string, promptId: string): Promise<Prompt | null> {
    const promptRef = doc(db, 'users', userId, 'prompts', promptId);
    const promptSnap = await getDoc(promptRef);
    
    if (promptSnap.exists()) {
      const data = promptSnap.data();
      return {
        id: promptSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      } as Prompt;
    }
    
    return null;
  },

  // Get all prompts for a user
  async getUserPrompts(userId: string, limitCount = 50): Promise<Prompt[]> {
    const promptsRef = collection(db, 'users', userId, 'prompts');
    const q = query(promptsRef, orderBy('updatedAt', 'desc'), limit(limitCount));
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || new Date()
    })) as Prompt[];
  },

  // Update a prompt
  async updatePrompt(userId: string, promptId: string, updates: Partial<Prompt>) {
    const promptRef = doc(db, 'users', userId, 'prompts', promptId);
    const updateData = {
      ...updates,
      updatedAt: serverTimestamp(),
      version: (updates.version || 1) + 1
    };
    
    await updateDoc(promptRef, updateData);
  },

  // Delete a prompt
  async deletePrompt(userId: string, promptId: string) {
    const promptRef = doc(db, 'users', userId, 'prompts', promptId);
    await deleteDoc(promptRef);
  },

  // Search prompts
  async searchPrompts(userId: string, searchParams: {
    category?: string;
    tags?: string[];
    isPublic?: boolean;
    limitCount?: number;
  }): Promise<Prompt[]> {
    const promptsRef = collection(db, 'users', userId, 'prompts');
    let q = query(promptsRef, orderBy('updatedAt', 'desc'));

    if (searchParams.category) {
      q = query(q, where('category', '==', searchParams.category));
    }

    if (searchParams.isPublic !== undefined) {
      q = query(q, where('isPublic', '==', searchParams.isPublic));
    }

    if (searchParams.tags && searchParams.tags.length > 0) {
      q = query(q, where('tags', 'array-contains-any', searchParams.tags));
    }

    if (searchParams.limitCount) {
      q = query(q, limit(searchParams.limitCount));
    }

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || new Date()
    })) as Prompt[];
  },

  // Subscribe to real-time updates
  subscribeToPrompts(userId: string, callback: (prompts: Prompt[]) => void) {
    const promptsRef = collection(db, 'users', userId, 'prompts');
    const q = query(promptsRef, orderBy('updatedAt', 'desc'));
    
    return onSnapshot(q, (snapshot) => {
      const prompts = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date()
      })) as Prompt[];
      
      callback(prompts);
    });
  }
};

// Execution operations
export const executionService = {
  // Create a new execution
  async createExecution(userId: string, promptId: string, executionData: Omit<PromptExecution, 'id' | 'timestamp'>) {
    const executionsRef = collection(db, 'users', userId, 'prompts', promptId, 'executions');
    const newExecution = {
      ...executionData,
      timestamp: serverTimestamp()
    };
    
    const docRef = await addDoc(executionsRef, newExecution);
    return docRef.id;
  },

  // Get executions for a prompt
  async getPromptExecutions(userId: string, promptId: string, limitCount = 20): Promise<PromptExecution[]> {
    const executionsRef = collection(db, 'users', userId, 'prompts', promptId, 'executions');
    const q = query(executionsRef, orderBy('timestamp', 'desc'), limit(limitCount));
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      timestamp: doc.data().timestamp?.toDate() || new Date()
    })) as PromptExecution[];
  },

  // Get all executions for a user
  async getUserExecutions(userId: string, limitCount = 50): Promise<PromptExecution[]> {
    // Note: This would require a collection group query in a real implementation
    // For now, we'll implement a simplified version
    const executions: PromptExecution[] = [];
    
    // Get all prompts first
    const prompts = await promptService.getUserPrompts(userId);
    
    // Get executions for each prompt (limited approach)
    for (const prompt of prompts.slice(0, 10)) { // Limit to first 10 prompts to avoid too many queries
      const promptExecutions = await this.getPromptExecutions(userId, prompt.id, 5);
      executions.push(...promptExecutions);
    }
    
    // Sort by timestamp and limit
    return executions
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limitCount);
  }
};

// RAG Document operations
export const documentService = {
  // Create a new document record
  async createDocument(userId: string, documentData: Omit<RAGDocument, 'id' | 'uploadedAt'>) {
    const documentsRef = collection(db, 'rag_documents');
    const newDocument = {
      ...documentData,
      uploadedBy: userId,
      uploadedAt: serverTimestamp()
    };
    
    const docRef = await addDoc(documentsRef, newDocument);
    return docRef.id;
  },

  // Get user's documents
  async getUserDocuments(userId: string): Promise<RAGDocument[]> {
    const documentsRef = collection(db, 'rag_documents');
    const q = query(documentsRef, where('uploadedBy', '==', userId), orderBy('uploadedAt', 'desc'));
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      uploadedAt: doc.data().uploadedAt?.toDate() || new Date(),
      processedAt: doc.data().processedAt?.toDate()
    })) as RAGDocument[];
  },

  // Update document status
  async updateDocumentStatus(documentId: string, status: RAGDocument['status'], error?: string) {
    const documentRef = doc(db, 'rag_documents', documentId);
    const updateData: any = {
      status,
      updatedAt: serverTimestamp()
    };
    
    if (status === 'completed') {
      updateData.processedAt = serverTimestamp();
    }
    
    if (error) {
      updateData.error = error;
    }
    
    await updateDoc(documentRef, updateData);
  }
};

// Workspace operations
export const workspaceService = {
  // Create a new workspace
  async createWorkspace(_userId: string, workspaceData: Omit<Workspace, 'id' | 'createdAt' | 'updatedAt'>) {
    const workspacesRef = collection(db, 'workspaces');
    const newWorkspace = {
      ...workspaceData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };
    
    const docRef = await addDoc(workspacesRef, newWorkspace);
    return docRef.id;
  },

  // Get user's workspaces
  async getUserWorkspaces(userId: string): Promise<Workspace[]> {
    const workspacesRef = collection(db, 'workspaces');
    const q = query(workspacesRef, where('members', 'array-contains', userId));
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || new Date()
    })) as Workspace[];
  }
};
