// Enhanced debugging script for prompt saving issues
// This script is automatically loaded and available in the browser console

// Initialize debug tools immediately
(function() {
  'use strict';

  console.log('🔧 Initializing Enhanced Debug Tools...');

window.debugPromptSaving = {
  // Test basic authentication with enhanced checks
  async testAuth() {
    console.log('🔍 Testing authentication...');
    try {
      const { auth } = await import('./config/firebase.js');
      const user = auth.currentUser;

      if (!user) {
        console.error('❌ No authenticated user found');
        console.log('💡 Try: Sign in to your account first');
        return false;
      }

      console.log('✅ User authenticated:', {
        uid: user.uid,
        email: user.email,
        emailVerified: user.emailVerified,
        creationTime: user.metadata.creationTime,
        lastSignInTime: user.metadata.lastSignInTime
      });

      // Test token refresh and validity
      const token = await user.getIdToken(true);
      const tokenResult = await user.getIdTokenResult();

      console.log('✅ Auth token details:', {
        tokenLength: token.length,
        expirationTime: tokenResult.expirationTime,
        authTime: tokenResult.authTime,
        issuedAtTime: tokenResult.issuedAtTime
      });

      return true;
    } catch (error) {
      console.error('❌ Authentication test failed:', error);
      console.log('💡 Try: Sign out and sign back in');
      return false;
    }
  },

  // Test Firestore connection with permission checks
  async testFirestore() {
    console.log('🔍 Testing Firestore connection and permissions...');
    try {
      const { db, auth } = await import('./config/firebase.js');
      const { doc, getDoc, collection, getDocs, query, limit } = await import('firebase/firestore');

      const user = auth.currentUser;
      if (!user) {
        console.error('❌ No authenticated user for Firestore test');
        return false;
      }

      // Test basic connection
      console.log('📡 Testing basic Firestore connection...');
      const userDocRef = doc(db, 'users', user.uid);
      const userDoc = await getDoc(userDocRef);

      console.log('✅ Basic Firestore connection successful:', {
        userDocExists: userDoc.exists(),
        projectId: db.app.options.projectId,
        host: db._delegate._databaseId.projectId
      });

      // Test prompts collection access
      console.log('📁 Testing prompts collection access...');
      const promptsRef = collection(db, 'users', user.uid, 'prompts');
      const promptsQuery = query(promptsRef, limit(1));
      const promptsSnapshot = await getDocs(promptsQuery);

      console.log('✅ Prompts collection access successful:', {
        canRead: true,
        promptCount: promptsSnapshot.size,
        collectionPath: promptsRef.path
      });

      return true;
    } catch (error) {
      console.error('❌ Firestore test failed:', error);
      if (error.code === 'permission-denied') {
        console.log('💡 Permission denied - check Firestore security rules');
        console.log('💡 Make sure you are signed in with the correct account');
      } else if (error.code === 'unavailable') {
        console.log('💡 Firestore service unavailable - check internet connection');
      }
      return false;
    }
  },

  // Test prompt creation with comprehensive validation
  async testPromptCreation() {
    console.log('🔍 Testing prompt creation with validation...');
    try {
      const { promptService } = await import('./services/firestore.js');
      const { auth } = await import('./config/firebase.js');

      const user = auth.currentUser;
      if (!user) {
        console.error('❌ No authenticated user for prompt creation test');
        return false;
      }

      const testPrompt = {
        title: `Debug Test Prompt ${Date.now()}`,
        content: 'This is a comprehensive test prompt created by the enhanced debug script to verify all aspects of prompt saving functionality.',
        description: 'Debug test prompt with comprehensive validation',
        category: 'General',
        tags: ['debug', 'test', 'validation'],
        isPublic: false,
        variables: [
          { name: 'testVar', description: 'Test variable', required: true }
        ]
      };

      console.log('📝 Creating test prompt with data:', {
        title: testPrompt.title,
        contentLength: testPrompt.content.length,
        category: testPrompt.category,
        tagsCount: testPrompt.tags.length,
        variablesCount: testPrompt.variables.length
      });

      const startTime = Date.now();
      const promptId = await promptService.createPrompt(user.uid, testPrompt);
      const creationTime = Date.now() - startTime;

      console.log(`✅ Prompt created successfully in ${creationTime}ms with ID:`, promptId);

      // Verify the prompt exists and data integrity
      console.log('🔍 Verifying prompt data integrity...');
      const savedPrompt = await promptService.getPrompt(user.uid, promptId);

      if (savedPrompt) {
        const dataIntegrity = {
          titleMatch: savedPrompt.title === testPrompt.title,
          contentMatch: savedPrompt.content === testPrompt.content,
          categoryMatch: savedPrompt.category === testPrompt.category,
          tagsMatch: JSON.stringify(savedPrompt.tags) === JSON.stringify(testPrompt.tags),
          hasCreatedAt: !!savedPrompt.createdAt,
          hasUpdatedAt: !!savedPrompt.updatedAt,
          hasCreatedBy: savedPrompt.createdBy === user.uid
        };

        console.log('✅ Prompt verification successful:', {
          title: savedPrompt.title,
          createdAt: savedPrompt.createdAt,
          dataIntegrity
        });

        const allDataValid = Object.values(dataIntegrity).every(Boolean);
        if (!allDataValid) {
          console.warn('⚠️ Data integrity issues detected:', dataIntegrity);
        }

        // Clean up - delete the test prompt
        await promptService.deletePrompt(user.uid, promptId);
        console.log('🗑️ Test prompt cleaned up successfully');

        return allDataValid;
      } else {
        console.error('❌ Prompt created but could not be retrieved');
        return false;
      }
    } catch (error) {
      console.error('❌ Prompt creation test failed:', error);
      console.log('💡 Check browser console for detailed error messages');
      console.log('💡 Verify prompt data meets validation requirements');
      return false;
    }
  },

  // Test real-time synchronization
  async testRealTimeSync() {
    console.log('🔍 Testing real-time synchronization...');
    try {
      const { promptService } = await import('./services/firestore.js');
      const { auth } = await import('./config/firebase.js');

      const user = auth.currentUser;
      if (!user) {
        console.error('❌ No authenticated user for real-time sync test');
        return false;
      }

      let updateCount = 0;
      let initialData = null;

      console.log('📡 Setting up real-time listener...');
      const unsubscribe = promptService.subscribeToPrompts(user.uid, (prompts) => {
        updateCount++;
        if (updateCount === 1) {
          initialData = prompts;
          console.log(`📊 Initial data received: ${prompts.length} prompts`);
        } else {
          console.log(`📡 Real-time update #${updateCount - 1}: ${prompts.length} prompts`);
        }
      });

      // Wait for initial data
      await new Promise(resolve => setTimeout(resolve, 3000));

      if (updateCount > 0) {
        console.log('✅ Real-time sync working correctly:', {
          updatesReceived: updateCount,
          initialPromptCount: initialData?.length || 0
        });
        unsubscribe();
        return true;
      } else {
        console.error('❌ No real-time updates received within timeout');
        unsubscribe();
        return false;
      }
    } catch (error) {
      console.error('❌ Real-time sync test failed:', error);
      return false;
    }
  },

  // Test prompt listing with performance metrics
  async testPromptListing() {
    console.log('🔍 Testing prompt listing with performance metrics...');
    try {
      const { promptService } = await import('./services/firestore.js');
      const { auth } = await import('./config/firebase.js');

      const user = auth.currentUser;
      if (!user) {
        console.error('❌ No authenticated user for prompt listing test');
        return false;
      }

      const startTime = Date.now();
      const prompts = await promptService.getUserPrompts(user.uid);
      const loadTime = Date.now() - startTime;

      console.log('✅ Retrieved prompts successfully:', {
        count: prompts.length,
        loadTime: `${loadTime}ms`,
        averageTimePerPrompt: prompts.length > 0 ? `${(loadTime / prompts.length).toFixed(2)}ms` : 'N/A',
        sampleTitles: prompts.slice(0, 5).map(p => p.title),
        categories: [...new Set(prompts.map(p => p.category))],
        totalTags: [...new Set(prompts.flatMap(p => p.tags))].length
      });

      // Performance analysis
      if (loadTime > 5000) {
        console.warn('⚠️ Slow loading detected - consider pagination or optimization');
      } else if (loadTime > 2000) {
        console.warn('⚠️ Moderate loading time - monitor performance');
      }

      return true;
    } catch (error) {
      console.error('❌ Prompt listing test failed:', error);
      return false;
    }
  },

  // Run comprehensive diagnostics
  async runAllTests() {
    console.log('🚀 Running comprehensive prompt saving diagnostics...');
    console.log('=' .repeat(60));

    const startTime = Date.now();

    const results = {
      auth: await this.testAuth(),
      firestore: await this.testFirestore(),
      creation: await this.testPromptCreation(),
      listing: await this.testPromptListing(),
      realTimeSync: await this.testRealTimeSync()
    };

    const totalTime = Date.now() - startTime;

    console.log('=' .repeat(60));
    console.log('📊 COMPREHENSIVE TEST RESULTS SUMMARY:');
    console.log(`⏱️ Total execution time: ${totalTime}ms`);
    console.log('');

    Object.entries(results).forEach(([test, passed]) => {
      const icon = passed ? '✅' : '❌';
      const status = passed ? 'PASSED' : 'FAILED';
      console.log(`${icon} ${test.toUpperCase()}: ${status}`);
    });

    const passedCount = Object.values(results).filter(Boolean).length;
    const totalCount = Object.keys(results).length;
    const allPassed = passedCount === totalCount;

    console.log('');
    console.log(`🎯 OVERALL STATUS: ${allPassed ? '✅ ALL TESTS PASSED' : `❌ ${passedCount}/${totalCount} TESTS PASSED`}`);

    if (!allPassed) {
      console.log('');
      console.log('🔧 TROUBLESHOOTING RECOMMENDATIONS:');

      if (!results.auth) {
        console.log('🔐 Authentication Issues:');
        console.log('  - Sign out and sign back in');
        console.log('  - Clear browser cache and cookies');
        console.log('  - Check if your session has expired');
      }

      if (!results.firestore) {
        console.log('🔥 Firestore Issues:');
        console.log('  - Check your internet connection');
        console.log('  - Verify Firestore security rules');
        console.log('  - Try refreshing the page');
      }

      if (!results.creation) {
        console.log('📝 Prompt Creation Issues:');
        console.log('  - Check browser console for detailed error messages');
        console.log('  - Verify prompt data meets validation requirements');
        console.log('  - Ensure proper authentication');
      }

      if (!results.listing) {
        console.log('📋 Prompt Listing Issues:');
        console.log('  - Check network connectivity');
        console.log('  - Verify database permissions');
      }

      if (!results.realTimeSync) {
        console.log('📡 Real-time Sync Issues:');
        console.log('  - Check WebSocket connectivity');
        console.log('  - Verify Firestore real-time features are enabled');
      }
    } else {
      console.log('');
      console.log('🎉 All systems operational! Prompt saving should work correctly.');
    }

    return results;
  },

  // Get comprehensive system information
  getSystemInfo() {
    console.log('🖥️ COMPREHENSIVE SYSTEM INFORMATION:');

    const info = {
      browser: {
        userAgent: navigator.userAgent,
        language: navigator.language,
        languages: navigator.languages,
        platform: navigator.platform,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine
      },
      features: {
        localStorage: 'localStorage' in window,
        sessionStorage: 'sessionStorage' in window,
        indexedDB: 'indexedDB' in window,
        webSocket: 'WebSocket' in window,
        fetch: 'fetch' in window,
        promises: 'Promise' in window,
        asyncAwait: (async () => {}).constructor === (async function(){}).constructor
      },
      performance: {
        memory: (performance).memory ? {
          usedJSHeapSize: (performance).memory.usedJSHeapSize,
          totalJSHeapSize: (performance).memory.totalJSHeapSize,
          jsHeapSizeLimit: (performance).memory.jsHeapSizeLimit
        } : 'Not available',
        timing: performance.timing ? {
          navigationStart: performance.timing.navigationStart,
          loadEventEnd: performance.timing.loadEventEnd,
          domContentLoadedEventEnd: performance.timing.domContentLoadedEventEnd
        } : 'Not available'
      },
      network: {
        connection: (navigator).connection ? {
          effectiveType: (navigator).connection.effectiveType,
          downlink: (navigator).connection.downlink,
          rtt: (navigator).connection.rtt,
          saveData: (navigator).connection.saveData
        } : 'Not available'
      },
      time: {
        currentTime: new Date().toISOString(),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        timezoneOffset: new Date().getTimezoneOffset()
      }
    };

    console.table(info.browser);
    console.table(info.features);
    console.log('Performance:', info.performance);
    console.log('Network:', info.network);
    console.log('Time:', info.time);

    return info;
  }
};

// Initialize enhanced diagnostics
console.log('🔧 Enhanced Debug Tools Loaded Successfully!');
console.log('');
console.log('📋 AVAILABLE COMMANDS:');
console.log('🔍 window.debugPromptSaving.runAllTests() - Run comprehensive diagnostics');
console.log('🔐 window.debugPromptSaving.testAuth() - Test authentication');
console.log('🔥 window.debugPromptSaving.testFirestore() - Test Firestore connection');
console.log('📝 window.debugPromptSaving.testPromptCreation() - Test prompt creation');
console.log('📋 window.debugPromptSaving.testPromptListing() - Test prompt retrieval');
console.log('📡 window.debugPromptSaving.testRealTimeSync() - Test real-time synchronization');
console.log('🖥️ window.debugPromptSaving.getSystemInfo() - Show system information');
console.log('');
console.log('💡 TIP: Start with runAllTests() for a complete diagnosis!');
