import { auth, db } from '../config/firebase';
import { promptService } from '../services/firestore';
import { collection, doc, getDoc, getDocs, query, orderBy, limit } from 'firebase/firestore';

export interface DiagnosticResult {
  test: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  details?: any;
  timestamp: string;
}

export class PromptSavingDiagnostics {
  private results: DiagnosticResult[] = [];
  
  private addResult(test: string, status: 'PASS' | 'FAIL' | 'WARNING', message: string, details?: any) {
    this.results.push({
      test,
      status,
      message,
      details,
      timestamp: new Date().toISOString()
    });
  }

  async runFullDiagnostics(): Promise<DiagnosticResult[]> {
    this.results = [];
    console.log('🔍 Starting comprehensive prompt saving diagnostics...');

    await this.checkAuthentication();
    await this.checkFirestoreConnection();
    await this.checkPermissions();
    await this.testPromptCreation();
    await this.testPromptRetrieval();
    await this.testRealTimeSync();
    await this.checkBrowserCompatibility();
    await this.checkNetworkConditions();

    console.log('✅ Diagnostics completed:', this.results);
    return this.results;
  }

  private async checkAuthentication() {
    try {
      const user = auth.currentUser;
      if (!user) {
        this.addResult('Authentication', 'FAIL', 'No authenticated user found');
        return;
      }

      // Check if auth token is valid
      const token = await user.getIdToken(true);
      if (!token) {
        this.addResult('Authentication', 'FAIL', 'Failed to get auth token');
        return;
      }

      // Check token claims
      const tokenResult = await user.getIdTokenResult();
      this.addResult('Authentication', 'PASS', `User authenticated: ${user.uid}`, {
        uid: user.uid,
        email: user.email,
        emailVerified: user.emailVerified,
        tokenExpiry: tokenResult.expirationTime,
        authTime: tokenResult.authTime
      });
    } catch (error: any) {
      this.addResult('Authentication', 'FAIL', `Auth error: ${error.message}`, error);
    }
  }

  private async checkFirestoreConnection() {
    try {
      const user = auth.currentUser;
      if (!user) {
        this.addResult('Firestore Connection', 'FAIL', 'Cannot test - no authenticated user');
        return;
      }

      // Test basic Firestore connectivity
      const testRef = doc(db, 'users', user.uid);
      const testDoc = await getDoc(testRef);
      
      this.addResult('Firestore Connection', 'PASS', 'Successfully connected to Firestore', {
        userDocExists: testDoc.exists(),
        firestoreHost: db.app.options.projectId
      });
    } catch (error: any) {
      this.addResult('Firestore Connection', 'FAIL', `Firestore connection error: ${error.message}`, error);
    }
  }

  private async checkPermissions() {
    try {
      const user = auth.currentUser;
      if (!user) {
        this.addResult('Permissions', 'FAIL', 'Cannot test - no authenticated user');
        return;
      }

      // Test read permissions
      const promptsRef = collection(db, 'users', user.uid, 'prompts');
      const testQuery = query(promptsRef, limit(1));
      const snapshot = await getDocs(testQuery);
      
      this.addResult('Permissions', 'PASS', 'Read permissions verified', {
        canRead: true,
        promptCount: snapshot.size
      });
    } catch (error: any) {
      if (error.code === 'permission-denied') {
        this.addResult('Permissions', 'FAIL', 'Permission denied - check Firestore security rules', error);
      } else {
        this.addResult('Permissions', 'FAIL', `Permission test error: ${error.message}`, error);
      }
    }
  }

  private async testPromptCreation() {
    try {
      const user = auth.currentUser;
      if (!user) {
        this.addResult('Prompt Creation', 'FAIL', 'Cannot test - no authenticated user');
        return;
      }

      const testPrompt = {
        title: `Diagnostic Test Prompt ${Date.now()}`,
        content: 'This is a test prompt created by the diagnostic tool.',
        description: 'Test prompt for diagnostics',
        category: 'General',
        tags: ['test', 'diagnostic'],
        isPublic: false,
        variables: []
      };

      const promptId = await promptService.createPrompt(user.uid, testPrompt);
      
      // Verify the prompt was created
      const savedPrompt = await promptService.getPrompt(user.uid, promptId);
      
      if (savedPrompt) {
        this.addResult('Prompt Creation', 'PASS', 'Successfully created and verified test prompt', {
          promptId,
          title: savedPrompt.title,
          createdAt: savedPrompt.createdAt
        });
        
        // Clean up test prompt
        await promptService.deletePrompt(user.uid, promptId);
      } else {
        this.addResult('Prompt Creation', 'FAIL', 'Prompt created but could not be retrieved');
      }
    } catch (error: any) {
      this.addResult('Prompt Creation', 'FAIL', `Prompt creation failed: ${error.message}`, error);
    }
  }

  private async testPromptRetrieval() {
    try {
      const user = auth.currentUser;
      if (!user) {
        this.addResult('Prompt Retrieval', 'FAIL', 'Cannot test - no authenticated user');
        return;
      }

      const prompts = await promptService.getUserPrompts(user.uid);
      
      this.addResult('Prompt Retrieval', 'PASS', `Successfully retrieved ${prompts.length} prompts`, {
        promptCount: prompts.length,
        sampleTitles: prompts.slice(0, 3).map(p => p.title)
      });
    } catch (error: any) {
      this.addResult('Prompt Retrieval', 'FAIL', `Prompt retrieval failed: ${error.message}`, error);
    }
  }

  private async testRealTimeSync() {
    try {
      const user = auth.currentUser;
      if (!user) {
        this.addResult('Real-time Sync', 'FAIL', 'Cannot test - no authenticated user');
        return;
      }

      let updateReceived = false;
      const unsubscribe = promptService.subscribeToPrompts(user.uid, (prompts) => {
        updateReceived = true;
        console.log('📡 Real-time update received:', prompts.length, 'prompts');
      });

      // Wait for initial data
      await new Promise(resolve => setTimeout(resolve, 2000));
      unsubscribe();

      if (updateReceived) {
        this.addResult('Real-time Sync', 'PASS', 'Real-time subscription working correctly');
      } else {
        this.addResult('Real-time Sync', 'WARNING', 'Real-time subscription did not receive data within timeout');
      }
    } catch (error: any) {
      this.addResult('Real-time Sync', 'FAIL', `Real-time sync test failed: ${error.message}`, error);
    }
  }

  private async checkBrowserCompatibility() {
    try {
      const compatibility = {
        indexedDB: 'indexedDB' in window,
        localStorage: 'localStorage' in window,
        fetch: 'fetch' in window,
        promises: 'Promise' in window,
        webSockets: 'WebSocket' in window,
        userAgent: navigator.userAgent
      };

      const issues = [];
      if (!compatibility.indexedDB) issues.push('IndexedDB not supported');
      if (!compatibility.localStorage) issues.push('LocalStorage not supported');
      if (!compatibility.fetch) issues.push('Fetch API not supported');
      if (!compatibility.promises) issues.push('Promises not supported');

      if (issues.length > 0) {
        this.addResult('Browser Compatibility', 'FAIL', `Browser compatibility issues: ${issues.join(', ')}`, compatibility);
      } else {
        this.addResult('Browser Compatibility', 'PASS', 'Browser fully compatible', compatibility);
      }
    } catch (error: any) {
      this.addResult('Browser Compatibility', 'FAIL', `Compatibility check failed: ${error.message}`, error);
    }
  }

  private async checkNetworkConditions() {
    try {
      const startTime = Date.now();
      
      // Test network latency with a simple Firestore operation
      const user = auth.currentUser;
      if (user) {
        const testRef = doc(db, 'users', user.uid);
        await getDoc(testRef);
      }
      
      const latency = Date.now() - startTime;
      
      const networkInfo = {
        latency: `${latency}ms`,
        online: navigator.onLine,
        connection: (navigator as any).connection ? {
          effectiveType: (navigator as any).connection.effectiveType,
          downlink: (navigator as any).connection.downlink,
          rtt: (navigator as any).connection.rtt
        } : 'Not available'
      };

      if (latency > 5000) {
        this.addResult('Network Conditions', 'WARNING', `High latency detected: ${latency}ms`, networkInfo);
      } else if (latency > 2000) {
        this.addResult('Network Conditions', 'WARNING', `Moderate latency: ${latency}ms`, networkInfo);
      } else {
        this.addResult('Network Conditions', 'PASS', `Good network conditions: ${latency}ms`, networkInfo);
      }
    } catch (error: any) {
      this.addResult('Network Conditions', 'FAIL', `Network test failed: ${error.message}`, error);
    }
  }

  generateReport(): string {
    const passCount = this.results.filter(r => r.status === 'PASS').length;
    const failCount = this.results.filter(r => r.status === 'FAIL').length;
    const warningCount = this.results.filter(r => r.status === 'WARNING').length;

    let report = `
🔍 PROMPT SAVING DIAGNOSTICS REPORT
Generated: ${new Date().toISOString()}

📊 SUMMARY:
✅ Passed: ${passCount}
❌ Failed: ${failCount}
⚠️  Warnings: ${warningCount}

📋 DETAILED RESULTS:
`;

    this.results.forEach(result => {
      const icon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️';
      report += `${icon} ${result.test}: ${result.message}\n`;
      if (result.details) {
        report += `   Details: ${JSON.stringify(result.details, null, 2)}\n`;
      }
      report += '\n';
    });

    return report;
  }
}

// Global diagnostic instance for browser console access
declare global {
  interface Window {
    promptDiagnostics: PromptSavingDiagnostics;
    runPromptDiagnostics: () => Promise<DiagnosticResult[]>;
  }
}

// Initialize global diagnostics
if (typeof window !== 'undefined') {
  window.promptDiagnostics = new PromptSavingDiagnostics();
  window.runPromptDiagnostics = async () => {
    const results = await window.promptDiagnostics.runFullDiagnostics();
    console.log(window.promptDiagnostics.generateReport());
    return results;
  };
}

export default PromptSavingDiagnostics;
