import React, { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'
import { initializeMonitoring } from './utils/monitoring'

// Import Firebase test functions for debugging
import { testFirebaseConnection } from './test-firebase-connection.js'
import { runComprehensiveTest } from './debug-prompt-saving.js'

// Register service worker for offline support and caching
if ('serviceWorker' in navigator && import.meta.env.PROD) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered: ', registration);
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}

// Initialize monitoring system
if (import.meta.env.PROD || import.meta.env.VITE_ENABLE_MONITORING === 'true') {
  initializeMonitoring();
}

// Make test functions available globally for debugging
if (import.meta.env.DEV) {
  window.testFirebaseConnection = testFirebaseConnection;
  window.runComprehensiveTest = runComprehensiveTest;
  console.log('🧪 Firebase test functions loaded for debugging');
  console.log('🔧 Run window.runComprehensiveTest() to test prompt saving');
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
