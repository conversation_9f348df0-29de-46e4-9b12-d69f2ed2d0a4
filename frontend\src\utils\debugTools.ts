// Enhanced debugging tools for prompt saving issues
// This TypeScript file will be properly imported and initialized

import { auth, db } from '../config/firebase';
import { promptService } from '../services/firestore';
import { collection, doc, getDoc, getDocs, query, limit } from 'firebase/firestore';

interface TestResult {
  name: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  details?: any;
  duration?: number;
}

class DebugTools {
  private results: TestResult[] = [];

  private addResult(name: string, status: 'PASS' | 'FAIL' | 'WARNING', message: string, details?: any, duration?: number) {
    this.results.push({ name, status, message, details, duration });
  }

  async testAuth(): Promise<boolean> {
    console.log('🔍 Testing authentication...');
    const startTime = Date.now();
    
    try {
      const user = auth.currentUser;
      
      if (!user) {
        this.addResult('Authentication', 'FAIL', 'No authenticated user found', null, Date.now() - startTime);
        console.error('❌ No authenticated user found');
        console.log('💡 Try: Sign in to your account first');
        return false;
      }
      
      console.log('✅ User authenticated:', {
        uid: user.uid,
        email: user.email,
        emailVerified: user.emailVerified,
        creationTime: user.metadata.creationTime,
        lastSignInTime: user.metadata.lastSignInTime
      });
      
      // Test token refresh and validity
      const token = await user.getIdToken(true);
      const tokenResult = await user.getIdTokenResult();
      
      const tokenDetails = {
        tokenLength: token.length,
        expirationTime: tokenResult.expirationTime,
        authTime: tokenResult.authTime,
        issuedAtTime: tokenResult.issuedAtTime
      };
      
      console.log('✅ Auth token details:', tokenDetails);
      
      this.addResult('Authentication', 'PASS', `User authenticated: ${user.uid}`, {
        user: { uid: user.uid, email: user.email, emailVerified: user.emailVerified },
        token: tokenDetails
      }, Date.now() - startTime);
      
      return true;
    } catch (error: any) {
      this.addResult('Authentication', 'FAIL', `Auth error: ${error.message}`, error, Date.now() - startTime);
      console.error('❌ Authentication test failed:', error);
      console.log('💡 Try: Sign out and sign back in');
      return false;
    }
  }

  async testFirestore(): Promise<boolean> {
    console.log('🔍 Testing Firestore connection and permissions...');
    const startTime = Date.now();
    
    try {
      const user = auth.currentUser;
      if (!user) {
        this.addResult('Firestore', 'FAIL', 'No authenticated user for Firestore test', null, Date.now() - startTime);
        console.error('❌ No authenticated user for Firestore test');
        return false;
      }
      
      // Test basic connection
      console.log('📡 Testing basic Firestore connection...');
      const userDocRef = doc(db, 'users', user.uid);
      const userDoc = await getDoc(userDocRef);
      
      const connectionInfo = {
        userDocExists: userDoc.exists(),
        projectId: db.app.options.projectId
      };
      
      console.log('✅ Basic Firestore connection successful:', connectionInfo);
      
      // Test prompts collection access
      console.log('📁 Testing prompts collection access...');
      const promptsRef = collection(db, 'users', user.uid, 'prompts');
      const promptsQuery = query(promptsRef, limit(1));
      const promptsSnapshot = await getDocs(promptsQuery);
      
      const accessInfo = {
        canRead: true,
        promptCount: promptsSnapshot.size,
        collectionPath: promptsRef.path
      };
      
      console.log('✅ Prompts collection access successful:', accessInfo);
      
      this.addResult('Firestore', 'PASS', 'Firestore connection and permissions verified', {
        connection: connectionInfo,
        access: accessInfo
      }, Date.now() - startTime);
      
      return true;
    } catch (error: any) {
      this.addResult('Firestore', 'FAIL', `Firestore test failed: ${error.message}`, error, Date.now() - startTime);
      console.error('❌ Firestore test failed:', error);
      
      if (error.code === 'permission-denied') {
        console.log('💡 Permission denied - check Firestore security rules');
        console.log('💡 Make sure you are signed in with the correct account');
      } else if (error.code === 'unavailable') {
        console.log('💡 Firestore service unavailable - check internet connection');
      }
      return false;
    }
  }

  async testPromptCreation(): Promise<boolean> {
    console.log('🔍 Testing prompt creation with validation...');
    const startTime = Date.now();
    
    try {
      const user = auth.currentUser;
      if (!user) {
        this.addResult('Prompt Creation', 'FAIL', 'No authenticated user for prompt creation test', null, Date.now() - startTime);
        console.error('❌ No authenticated user for prompt creation test');
        return false;
      }
      
      const testPrompt = {
        title: `Debug Test Prompt ${Date.now()}`,
        content: 'This is a comprehensive test prompt created by the enhanced debug script to verify all aspects of prompt saving functionality.',
        description: 'Debug test prompt with comprehensive validation',
        category: 'General',
        tags: ['debug', 'test', 'validation'],
        isPublic: false,
        variables: [
          { name: 'testVar', description: 'Test variable', required: true }
        ]
      };
      
      console.log('📝 Creating test prompt with data:', {
        title: testPrompt.title,
        contentLength: testPrompt.content.length,
        category: testPrompt.category,
        tagsCount: testPrompt.tags.length,
        variablesCount: testPrompt.variables.length
      });
      
      const creationStartTime = Date.now();
      const promptId = await promptService.createPrompt(user.uid, testPrompt);
      const creationTime = Date.now() - creationStartTime;
      
      console.log(`✅ Prompt created successfully in ${creationTime}ms with ID:`, promptId);
      
      // Verify the prompt exists and data integrity
      console.log('🔍 Verifying prompt data integrity...');
      const savedPrompt = await promptService.getPrompt(user.uid, promptId);
      
      if (savedPrompt) {
        const dataIntegrity = {
          titleMatch: savedPrompt.title === testPrompt.title,
          contentMatch: savedPrompt.content === testPrompt.content,
          categoryMatch: savedPrompt.category === testPrompt.category,
          tagsMatch: JSON.stringify(savedPrompt.tags) === JSON.stringify(testPrompt.tags),
          hasCreatedAt: !!savedPrompt.createdAt,
          hasUpdatedAt: !!savedPrompt.updatedAt,
          hasCreatedBy: savedPrompt.createdBy === user.uid
        };
        
        console.log('✅ Prompt verification successful:', {
          title: savedPrompt.title,
          createdAt: savedPrompt.createdAt,
          dataIntegrity
        });
        
        const allDataValid = Object.values(dataIntegrity).every(Boolean);
        if (!allDataValid) {
          console.warn('⚠️ Data integrity issues detected:', dataIntegrity);
        }
        
        // Clean up - delete the test prompt
        await promptService.deletePrompt(user.uid, promptId);
        console.log('🗑️ Test prompt cleaned up successfully');
        
        this.addResult('Prompt Creation', allDataValid ? 'PASS' : 'WARNING', 
          allDataValid ? 'Prompt creation and verification successful' : 'Prompt created but data integrity issues detected',
          { promptId, creationTime, dataIntegrity }, Date.now() - startTime);
        
        return allDataValid;
      } else {
        this.addResult('Prompt Creation', 'FAIL', 'Prompt created but could not be retrieved', { promptId }, Date.now() - startTime);
        console.error('❌ Prompt created but could not be retrieved');
        return false;
      }
    } catch (error: any) {
      this.addResult('Prompt Creation', 'FAIL', `Prompt creation failed: ${error.message}`, error, Date.now() - startTime);
      console.error('❌ Prompt creation test failed:', error);
      console.log('💡 Check browser console for detailed error messages');
      console.log('💡 Verify prompt data meets validation requirements');
      return false;
    }
  }

  async testPromptListing(): Promise<boolean> {
    console.log('🔍 Testing prompt listing with performance metrics...');
    const startTime = Date.now();
    
    try {
      const user = auth.currentUser;
      if (!user) {
        this.addResult('Prompt Listing', 'FAIL', 'No authenticated user for prompt listing test', null, Date.now() - startTime);
        console.error('❌ No authenticated user for prompt listing test');
        return false;
      }
      
      const listingStartTime = Date.now();
      const prompts = await promptService.getUserPrompts(user.uid);
      const loadTime = Date.now() - listingStartTime;
      
      const results = {
        count: prompts.length,
        loadTime: `${loadTime}ms`,
        averageTimePerPrompt: prompts.length > 0 ? `${(loadTime / prompts.length).toFixed(2)}ms` : 'N/A',
        sampleTitles: prompts.slice(0, 5).map(p => p.title),
        categories: [...new Set(prompts.map(p => p.category))],
        totalTags: [...new Set(prompts.flatMap(p => p.tags))].length
      };
      
      console.log('✅ Retrieved prompts successfully:', results);
      
      // Performance analysis
      let status: 'PASS' | 'WARNING' = 'PASS';
      if (loadTime > 5000) {
        console.warn('⚠️ Slow loading detected - consider pagination or optimization');
        status = 'WARNING';
      } else if (loadTime > 2000) {
        console.warn('⚠️ Moderate loading time - monitor performance');
        status = 'WARNING';
      }
      
      this.addResult('Prompt Listing', status, `Retrieved ${prompts.length} prompts in ${loadTime}ms`, results, Date.now() - startTime);
      
      return true;
    } catch (error: any) {
      this.addResult('Prompt Listing', 'FAIL', `Prompt listing failed: ${error.message}`, error, Date.now() - startTime);
      console.error('❌ Prompt listing test failed:', error);
      return false;
    }
  }

  async testRealTimeSync(): Promise<boolean> {
    console.log('🔍 Testing real-time synchronization...');
    const startTime = Date.now();
    
    try {
      const user = auth.currentUser;
      if (!user) {
        this.addResult('Real-time Sync', 'FAIL', 'No authenticated user for real-time sync test', null, Date.now() - startTime);
        console.error('❌ No authenticated user for real-time sync test');
        return false;
      }
      
      let updateCount = 0;
      let initialData: any = null;
      
      console.log('📡 Setting up real-time listener...');
      const unsubscribe = promptService.subscribeToPrompts(user.uid, (prompts) => {
        updateCount++;
        if (updateCount === 1) {
          initialData = prompts;
          console.log(`📊 Initial data received: ${prompts.length} prompts`);
        } else {
          console.log(`📡 Real-time update #${updateCount - 1}: ${prompts.length} prompts`);
        }
      });
      
      // Wait for initial data
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const syncResults = {
        updatesReceived: updateCount,
        initialPromptCount: initialData?.length || 0
      };
      
      if (updateCount > 0) {
        console.log('✅ Real-time sync working correctly:', syncResults);
        this.addResult('Real-time Sync', 'PASS', 'Real-time synchronization working correctly', syncResults, Date.now() - startTime);
        unsubscribe();
        return true;
      } else {
        console.error('❌ No real-time updates received within timeout');
        this.addResult('Real-time Sync', 'FAIL', 'No real-time updates received within timeout', syncResults, Date.now() - startTime);
        unsubscribe();
        return false;
      }
    } catch (error: any) {
      this.addResult('Real-time Sync', 'FAIL', `Real-time sync test failed: ${error.message}`, error, Date.now() - startTime);
      console.error('❌ Real-time sync test failed:', error);
      return false;
    }
  }

  async runAllTests(): Promise<{ [key: string]: boolean }> {
    console.log('🚀 Running comprehensive prompt saving diagnostics...');
    console.log('=' .repeat(60));
    
    this.results = []; // Clear previous results
    const overallStartTime = Date.now();
    
    const results = {
      auth: await this.testAuth(),
      firestore: await this.testFirestore(),
      creation: await this.testPromptCreation(),
      listing: await this.testPromptListing(),
      realTimeSync: await this.testRealTimeSync()
    };
    
    const totalTime = Date.now() - overallStartTime;
    
    console.log('=' .repeat(60));
    console.log('📊 COMPREHENSIVE TEST RESULTS SUMMARY:');
    console.log(`⏱️ Total execution time: ${totalTime}ms`);
    console.log('');
    
    Object.entries(results).forEach(([test, passed]) => {
      const icon = passed ? '✅' : '❌';
      const status = passed ? 'PASSED' : 'FAILED';
      console.log(`${icon} ${test.toUpperCase()}: ${status}`);
    });
    
    const passedCount = Object.values(results).filter(Boolean).length;
    const totalCount = Object.keys(results).length;
    const allPassed = passedCount === totalCount;
    
    console.log('');
    console.log(`🎯 OVERALL STATUS: ${allPassed ? '✅ ALL TESTS PASSED' : `❌ ${passedCount}/${totalCount} TESTS PASSED`}`);
    
    if (!allPassed) {
      console.log('');
      console.log('🔧 TROUBLESHOOTING RECOMMENDATIONS:');
      
      if (!results.auth) {
        console.log('🔐 Authentication Issues:');
        console.log('  - Sign out and sign back in');
        console.log('  - Clear browser cache and cookies');
        console.log('  - Check if your session has expired');
      }
      
      if (!results.firestore) {
        console.log('🔥 Firestore Issues:');
        console.log('  - Check your internet connection');
        console.log('  - Verify Firestore security rules');
        console.log('  - Try refreshing the page');
      }
      
      if (!results.creation) {
        console.log('📝 Prompt Creation Issues:');
        console.log('  - Check browser console for detailed error messages');
        console.log('  - Verify prompt data meets validation requirements');
        console.log('  - Ensure proper authentication');
      }
      
      if (!results.listing) {
        console.log('📋 Prompt Listing Issues:');
        console.log('  - Check network connectivity');
        console.log('  - Verify database permissions');
      }
      
      if (!results.realTimeSync) {
        console.log('📡 Real-time Sync Issues:');
        console.log('  - Check WebSocket connectivity');
        console.log('  - Verify Firestore real-time features are enabled');
      }
    } else {
      console.log('');
      console.log('🎉 All systems operational! Prompt saving should work correctly.');
    }
    
    return results;
  }

  getSystemInfo() {
    console.log('🖥️ COMPREHENSIVE SYSTEM INFORMATION:');
    
    const info = {
      browser: {
        userAgent: navigator.userAgent,
        language: navigator.language,
        languages: navigator.languages,
        platform: navigator.platform,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine
      },
      features: {
        localStorage: 'localStorage' in window,
        sessionStorage: 'sessionStorage' in window,
        indexedDB: 'indexedDB' in window,
        webSocket: 'WebSocket' in window,
        fetch: 'fetch' in window,
        promises: 'Promise' in window
      },
      performance: {
        memory: (performance as any).memory ? {
          usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
          totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
          jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
        } : 'Not available'
      },
      network: {
        connection: (navigator as any).connection ? {
          effectiveType: (navigator as any).connection.effectiveType,
          downlink: (navigator as any).connection.downlink,
          rtt: (navigator as any).connection.rtt,
          saveData: (navigator as any).connection.saveData
        } : 'Not available'
      },
      time: {
        currentTime: new Date().toISOString(),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        timezoneOffset: new Date().getTimezoneOffset()
      }
    };
    
    console.table(info.browser);
    console.table(info.features);
    console.log('Performance:', info.performance);
    console.log('Network:', info.network);
    console.log('Time:', info.time);
    
    return info;
  }

  getResults(): TestResult[] {
    return this.results;
  }
}

// Create global instance
const debugTools = new DebugTools();

// Export for module use
export { debugTools, DebugTools };

// Make available globally for browser console
declare global {
  interface Window {
    debugPromptSaving: DebugTools;
    runPromptDiagnostics: () => Promise<{ [key: string]: boolean }>;
  }
}

if (typeof window !== 'undefined') {
  window.debugPromptSaving = debugTools;
  window.runPromptDiagnostics = () => debugTools.runAllTests();
  
  console.log('🔧 Enhanced Debug Tools Loaded Successfully!');
  console.log('');
  console.log('📋 AVAILABLE COMMANDS:');
  console.log('🔍 window.debugPromptSaving.runAllTests() - Run comprehensive diagnostics');
  console.log('🔍 window.runPromptDiagnostics() - Same as above (shortcut)');
  console.log('🔐 window.debugPromptSaving.testAuth() - Test authentication');
  console.log('🔥 window.debugPromptSaving.testFirestore() - Test Firestore connection');
  console.log('📝 window.debugPromptSaving.testPromptCreation() - Test prompt creation');
  console.log('📋 window.debugPromptSaving.testPromptListing() - Test prompt retrieval');
  console.log('📡 window.debugPromptSaving.testRealTimeSync() - Test real-time synchronization');
  console.log('🖥️ window.debugPromptSaving.getSystemInfo() - Show system information');
  console.log('');
  console.log('💡 TIP: Start with runAllTests() for a complete diagnosis!');
}
