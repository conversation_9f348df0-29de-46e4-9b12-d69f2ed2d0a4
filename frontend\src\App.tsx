import React, { Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { UserProfileProvider } from './contexts/UserProfileContext';
import { WorkspaceProvider } from './contexts/WorkspaceContext';
import { AuthErrorBoundary } from './components/common/AuthErrorBoundary';
import { AuthPage } from './components/auth/AuthPage';
import { Layout } from './components/layout/Layout';
import { LoadingSpinner } from './components/common/LoadingSpinner';
import { ErrorBoundary } from './components/common/ErrorBoundary';
import { ToastProvider } from './components/common/Toast';
import { HelpProvider } from './components/help/HelpSystem';
import { PerformanceMonitor, PerformanceWarning } from './components/dev/PerformanceMonitor';
import { APIPerformanceMonitor, APIPerformanceAlerts } from './components/dev/APIPerformanceMonitor';
import { WebVitalsDashboard } from './components/dev/WebVitalsDashboard';
import { PerformanceDashboard } from './components/dev/PerformanceDashboard';
import { queryClient, backgroundSync } from './lib/queryClient';

// Import diagnostics tools for development
import './utils/promptSavingDiagnostics';
import './utils/debugTools';

// Lazy load heavy pages for better performance
const Dashboard = lazy(() => import('./pages/Dashboard').then(m => ({ default: m.Dashboard })));
const Prompts = lazy(() => import('./pages/Prompts').then(m => ({ default: m.Prompts })));
const Documents = lazy(() => import('./pages/Documents').then(m => ({ default: m.Documents })));
const Executions = lazy(() => import('./pages/Executions').then(m => ({ default: m.Executions })));
const ExecutePrompt = lazy(() => import('./pages/ExecutePrompt').then(m => ({ default: m.ExecutePrompt })));
const Analytics = lazy(() => import('./pages/Analytics').then(m => ({ default: m.Analytics })));
const Workspaces = lazy(() => import('./pages/Workspaces').then(m => ({ default: m.Workspaces })));
const Settings = lazy(() => import('./pages/Settings').then(m => ({ default: m.Settings })));
const BetaSignup = lazy(() => import('./pages/BetaSignup').then(m => ({ default: m.BetaSignup })));
const BetaProgram = lazy(() => import('./pages/BetaProgram'));
const Marketplace = lazy(() => import('./pages/Marketplace').then(m => ({ default: m.Marketplace })));
const HelpCenter = lazy(() => import('./components/help/HelpCenter').then(m => ({ default: m.HelpCenter })));

// Protected Route component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { currentUser, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return currentUser ? <>{children}</> : <Navigate to="/auth" />;
};

// Public Route component (redirect to dashboard if authenticated)
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { currentUser, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return currentUser ? <Navigate to="/" /> : <>{children}</>;
};

function App() {
  // Enable background sync for offline support
  React.useEffect(() => {
    backgroundSync.enable();
    return () => backgroundSync.disable();
  }, []);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ToastProvider>
          <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
            <AuthProvider>
              <UserProfileProvider>
                <WorkspaceProvider>
                  <HelpProvider>
                <div className="App">
              <Routes>
                {/* Public routes */}
                <Route
                  path="/auth"
                  element={
                    <PublicRoute>
                      <AuthPage />
                    </PublicRoute>
                  }
                />

                {/* Beta signup route */}
                <Route path="/beta-signup" element={
                  <Suspense fallback={<LoadingSpinner />}>
                    <BetaSignup />
                  </Suspense>
                } />

                {/* Beta program route */}
                <Route path="/beta" element={
                  <Suspense fallback={<LoadingSpinner />}>
                    <BetaProgram />
                  </Suspense>
                } />

                {/* Protected routes */}
                <Route
                  path="/"
                  element={
                    <ProtectedRoute>
                      <Layout />
                    </ProtectedRoute>
                  }
                >
                  <Route index element={
                    <Suspense fallback={<LoadingSpinner />}>
                      <Dashboard />
                    </Suspense>
                  } />
                  <Route path="prompts" element={
                    <Suspense fallback={<LoadingSpinner />}>
                      <Prompts />
                    </Suspense>
                  } />
                  <Route path="prompts/:promptId/execute" element={
                    <Suspense fallback={<LoadingSpinner />}>
                      <ExecutePrompt />
                    </Suspense>
                  } />
                  <Route path="documents" element={
                    <Suspense fallback={<LoadingSpinner />}>
                      <Documents />
                    </Suspense>
                  } />
                  <Route path="executions" element={
                    <Suspense fallback={<LoadingSpinner />}>
                      <Executions />
                    </Suspense>
                  } />
                  <Route path="analytics" element={
                    <Suspense fallback={<LoadingSpinner />}>
                      <Analytics />
                    </Suspense>
                  } />
                  <Route path="workspaces" element={
                    <Suspense fallback={<LoadingSpinner />}>
                      <Workspaces />
                    </Suspense>
                  } />
                  <Route path="marketplace" element={
                    <Suspense fallback={<LoadingSpinner />}>
                      <Marketplace />
                    </Suspense>
                  } />
                  <Route path="help" element={
                    <Suspense fallback={<LoadingSpinner />}>
                      <HelpCenter />
                    </Suspense>
                  } />
                  <Route path="settings" element={
                    <Suspense fallback={<LoadingSpinner />}>
                      <Settings />
                    </Suspense>
                  } />
                </Route>

                {/* Catch all route */}
                <Route path="*" element={<Navigate to="/" />} />
              </Routes>
              </div>
                </HelpProvider>
              </WorkspaceProvider>
            </UserProfileProvider>
          </AuthProvider>
        </Router>

        {/* Development Performance Monitoring - Only in development */}
        {import.meta.env.DEV && (
          <>
            <PerformanceMonitor />
            <PerformanceWarning />
            <APIPerformanceMonitor />
            <APIPerformanceAlerts />
            <WebVitalsDashboard />
            <PerformanceDashboard />
          </>
        )}
      </ToastProvider>

      {/* React Query DevTools - only in development */}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  </ErrorBoundary>
);
}

export default App;
